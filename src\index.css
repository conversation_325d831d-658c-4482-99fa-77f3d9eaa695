
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 210 100% 50%;
    --primary-foreground: 210 40% 98%;

    --secondary: 186 100% 42%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 186 100% 96%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 210 100% 50%;

    --radius: 1rem;

    --sidebar-background: 210 100% 14%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 186 100% 42%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 210 100% 20%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 210 100% 20%;
    --sidebar-ring: 186 100% 42%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 100% 60%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 186 100% 50%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 210 100% 60%;
    
    --sidebar-background: 222.2 84% 4.9%;
    --sidebar-foreground: 210 40% 98%;
    --sidebar-primary: 186 100% 50%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 217.2 32.6% 17.5%;
    --sidebar-accent-foreground: 210 40% 98%;
    --sidebar-border: 217.2 32.6% 17.5%;
    --sidebar-ring: 186 100% 50%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground antialiased;
  }
  
  /* Modern scrollbars */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }
  
  ::-webkit-scrollbar-track {
    @apply bg-transparent rounded-full;
  }
  
  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/20 rounded-full hover:bg-muted-foreground/30 transition-colors;
  }
}

@layer components {
  .glass-card {
    @apply bg-white/80 dark:bg-black/50 backdrop-blur-lg border border-white/10 dark:border-white/5 shadow-lg;
  }
  
  .hover-card {
    @apply transition-all duration-200 ease-in-out hover:shadow-md hover:-translate-y-1;
  }
  
  .status-badge {
    @apply inline-flex items-center rounded-full px-2.5 py-1 text-xs font-medium;
  }
  
  .status-pulse {
    @apply relative flex h-2 w-2 mr-1.5;
  }
  
  .status-pulse::before {
    @apply animate-ping absolute inline-flex h-full w-full rounded-full opacity-75 content-[''];
  }
}

@layer utilities {
  .animate-fade-in {
    animation: fadeIn 0.5s ease-out forwards;
  }
  
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(8px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}
