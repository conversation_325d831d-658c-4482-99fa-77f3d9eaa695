import React from 'react';
import { ReadyForDeliveryList } from '@/components/delivery/ReadyForDeliveryList';
import { PaidOrdersList } from '@/components/delivery/PaidOrdersList';

const Deliveries: React.FC = () => {
  return (
    <div className="space-y-6 animate-fade-in">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-3xl font-bold tracking-tight">Gestão de Entregas</h2>
          <p className="text-muted-foreground">
            Agende entregas de equipamentos reparados - o pagamento será coletado pelo entregador
          </p>
        </div>
      </div>

      {/* Lista de equipamentos prontos para entrega */}
      <ReadyForDeliveryList />

      {/* Lista de equipamentos com entrega agendada */}
      <PaidOrdersList />
    </div>
  );
};

export default Deliveries;
