<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Teste Supabase</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Teste de Conectividade Supabase</h1>
        
        <div id="status" class="status info">
            Aguardando teste...
        </div>
        
        <button onclick="testSupabase()">🧪 Testar Supabase</button>
        <button onclick="testServiceOrders()">📋 Testar Service Orders</button>
        <button onclick="clearResults()">🗑️ Limpar</button>
        
        <div id="results"></div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <script>
        // Configuração do Supabase
        const SUPABASE_URL = "https://hdyucwabemspehokoiks.supabase.co";
        const SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImhkeXVjd2FiZW1zcGVob2tvaWtzIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0NDA0MDc2OSwiZXhwIjoyMDU5NjE2NzY5fQ.G_2PF8hXeXIfl59xmywqpGdWiJC6JEVHFwJkoyBSWc0";
        
        const supabase = window.supabase.createClient(SUPABASE_URL, SUPABASE_KEY);
        
        function updateStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = `status ${type}`;
        }
        
        function addResult(title, data, type = 'info') {
            const resultsEl = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.innerHTML = `
                <h3>${title}</h3>
                <div class="status ${type}">
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                </div>
            `;
            resultsEl.appendChild(resultDiv);
        }
        
        async function testSupabase() {
            updateStatus('🔄 Testando conexão com Supabase...', 'info');
            
            try {
                // Teste básico de conectividade
                console.log('🧪 Iniciando teste de conectividade Supabase');
                console.log('🔗 URL:', SUPABASE_URL);
                console.log('🔑 Key:', SUPABASE_KEY.substring(0, 20) + '...');
                
                // Teste 1: Verificar se o cliente foi criado
                addResult('✅ Cliente Supabase', {
                    url: SUPABASE_URL,
                    keyPrefix: SUPABASE_KEY.substring(0, 20) + '...',
                    client: !!supabase
                }, 'success');
                
                // Teste 2: Fazer uma consulta simples
                const { data, error } = await supabase
                    .from('service_orders')
                    .select('id, client_name, status')
                    .limit(5);
                
                if (error) {
                    console.error('❌ Erro na consulta:', error);
                    addResult('❌ Erro na Consulta', error, 'error');
                    updateStatus('❌ Erro ao conectar com Supabase', 'error');
                } else {
                    console.log('✅ Consulta bem-sucedida:', data);
                    addResult('✅ Consulta Bem-sucedida', {
                        totalRecords: data.length,
                        records: data
                    }, 'success');
                    updateStatus('✅ Supabase funcionando corretamente!', 'success');
                }
                
            } catch (error) {
                console.error('💥 Erro geral:', error);
                addResult('💥 Erro Geral', error, 'error');
                updateStatus('💥 Erro geral na conexão', 'error');
            }
        }
        
        async function testServiceOrders() {
            updateStatus('🔄 Testando consulta completa de service orders...', 'info');
            
            try {
                const { data, error } = await supabase
                    .from('service_orders')
                    .select(`
                        *,
                        client:client_id (
                            email,
                            phone,
                            cpf_cnpj,
                            address_complement,
                            address_reference
                        ),
                        service_order_images (*),
                        service_items (*)
                    `)
                    .eq('archived', false);
                
                if (error) {
                    console.error('❌ Erro na consulta completa:', error);
                    addResult('❌ Erro na Consulta Completa', error, 'error');
                    updateStatus('❌ Erro na consulta completa', 'error');
                } else {
                    console.log('✅ Consulta completa bem-sucedida:', data);
                    addResult('✅ Service Orders Completa', {
                        totalRecords: data.length,
                        sampleRecord: data[0] || 'Nenhum registro encontrado'
                    }, 'success');
                    updateStatus('✅ Consulta completa funcionando!', 'success');
                }
                
            } catch (error) {
                console.error('💥 Erro na consulta completa:', error);
                addResult('💥 Erro na Consulta Completa', error, 'error');
                updateStatus('💥 Erro na consulta completa', 'error');
            }
        }
        
        function clearResults() {
            document.getElementById('results').innerHTML = '';
            updateStatus('Aguardando teste...', 'info');
        }
        
        // Informações do ambiente
        window.addEventListener('load', () => {
            addResult('🌐 Informações do Ambiente', {
                hostname: window.location.hostname,
                port: window.location.port,
                protocol: window.location.protocol,
                userAgent: navigator.userAgent.substring(0, 100) + '...'
            }, 'info');
        });
    </script>
</body>
</html>
