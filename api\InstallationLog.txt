************************************* Invoked: Tue May 13 00:49:37 2025
[0] Arguments: C:\Users\<USER>\AppData\Local\Temp\WinGet\MSYS2.MSYS2.20250221\msys2-x86_64-20250221.exe, install, --confirm-command, --root, C:\msys64
[7] Operations sanity check succeeded.
[14] Using metadata cache from "C:/Users/<USER>/AppData/Local/cache\\qt-installer-framework\\d75f1c19-3379-3717-ae8d-1404b51494a9"
[14] Found 0 cached items.
[19] Loaded control script ":/metadata/installer-config/control_js.js"
[21] TargetDirectoryInUse : Error : The directory you selected already exists and contains an installation. Choose a different target for installation.
