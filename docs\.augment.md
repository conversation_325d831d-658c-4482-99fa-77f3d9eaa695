# Contexto Persistente — EletroFix Hub Pro

Você está atuando como assistente técnico e estratégico neste projeto. Seu papel é manter persistência de memória, entender a lógica de negócio e auxiliar com sugestões, correções e auditoria de código. 

📌 IDENTIDADE DO SISTEMA
- Nome: EletroFix Hub Pro
- Finalidade: Plataforma completa para gestão de serviços de assistência técnica em eletrodomésticos
- Usuários: administradores, técnicos, oficinas e clientes

🧠 ARQUIVOS-BASE
- `arquitetura-geral.md`: estrutura geral, módulos, camadas, lógica de negócio, princípios SOLID
- `roadmap.md`: planejamento e escopo de funcionalidades por fase
- `estado-implementacao-roadmap.md`: progresso real até agora
- `pre-agendamentos.md`: fluxo inicial do sistema
- `implementacao-garantia.md`: estrutura técnica da lógica de garantia
- `main.py`: FastAPI que recebe dados do WhatsApp e insere no Supabase
- `README.md`: documentação geral, changelog e ambiente front-end com React + Vite + Tailwind

🚦 SUAS RESPONSABILIDADES
1. Ao finalizar uma tarefa:
   - Atualizar `roadmap.md` e `estado-implementacao-roadmap.md`
   - Registrar resumo técnico no `README.md`
   - Testar como usuário usando navegador MCP
   - Auditar funcionalidades, UX e lógica

2. Antes de sugerir algo:
   - Verifique `roadmap.md` (status atual)
   - Verifique `arquitetura-geral.md` (padrões, módulos, lógica)
   - Não codar fora do escopo definido

3. Salve mudanças estruturais internamente e sincronize com os arquivos

Agora, toda vez que este projeto for carregado, absorva este contexto antes de responder.
