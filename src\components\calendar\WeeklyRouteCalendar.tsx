import React, { useState, useMemo } from 'react';
import { format, startOfWeek, addDays, isSameDay, parseISO, addWeeks, subWeeks } from 'date-fns';
import { ptBR } from 'date-fns/locale';
import { ChevronLeft, ChevronRight, Calendar as CalendarIcon, Clock, AlertTriangle, GripVertical, User } from 'lucide-react';
import { DisplayNumber } from '@/components/common/DisplayNumber';

interface AgendamentoWithSchedule {
  id: number;
  nome: string;
  scheduledTime?: string;
  isSelected: boolean;
  sequenceOrder?: number;
}

interface WeeklyRouteCalendarProps {
  selectedDate?: Date;
  onDateSelect: (date: Date) => void;
  availabilityData?: any;
  isLoading?: boolean;
  technicianId?: string;
  className?: string;
  onAgendamentoDrop?: (agendamentoId: number, date: Date, time: string) => void;
  onAgendamentoDragStart?: (agendamento: AgendamentoWithSchedule) => void;
  onAgendamentoDragEnd?: () => void;
  draggedAgendamento?: { id: number; name: string } | null;
  agendamentos?: AgendamentoWithSchedule[];
  selectedDateString?: string;
}

const WeeklyRouteCalendar: React.FC<WeeklyRouteCalendarProps> = ({
  selectedDate,
  onDateSelect,
  availabilityData,
  isLoading,
  technicianId,
  className = '',
  onAgendamentoDrop,
  onAgendamentoDragStart,
  onAgendamentoDragEnd,
  draggedAgendamento,
  agendamentos = [],
  selectedDateString
}) => {
  const [currentWeek, setCurrentWeek] = useState(() => {
    return selectedDate ? startOfWeek(selectedDate, { weekStartsOn: 1 }) : startOfWeek(new Date(), { weekStartsOn: 1 });
  });

  // Estados para drag & drop
  const [dragOverSlot, setDragOverSlot] = useState<{ date: Date; time: string } | null>(null);
  const [isDragging, setIsDragging] = useState(false);

  // Gerar horários de 6:00 às 18:00
  const timeSlots = useMemo(() => {
    const slots = [];
    for (let hour = 6; hour <= 18; hour++) {
      slots.push(`${hour.toString().padStart(2, '0')}:00`);
    }
    return slots;
  }, []);

  // Gerar dias da semana
  const weekDays = useMemo(() => {
    const days = [];
    for (let i = 0; i < 7; i++) {
      const day = addDays(currentWeek, i);
      days.push(day);
    }
    return days;
  }, [currentWeek]);

  const navigateWeek = (direction: 'prev' | 'next') => {
    setCurrentWeek(prev => direction === 'prev' ? subWeeks(prev, 1) : addWeeks(prev, 1));
  };

  const handleDateTimeClick = (date: Date, time: string) => {
    const [hours, minutes] = time.split(':').map(Number);
    const dateTime = new Date(date);
    dateTime.setHours(hours, minutes, 0, 0);
    onDateSelect(dateTime);
  };

  const isSelectedDateTime = (date: Date, time: string) => {
    if (!selectedDate) return false;
    const [hours] = time.split(':').map(Number);
    return isSameDay(date, selectedDate) && selectedDate.getHours() === hours;
  };

  const isLunchTime = (time: string) => {
    return time === '12:00';
  };

  const isPastDateTime = (date: Date, time: string) => {
    const now = new Date();
    const [hours] = time.split(':').map(Number);
    const dateTime = new Date(date);
    dateTime.setHours(hours, 0, 0, 0);
    return dateTime < now;
  };

  // Função para obter agendamentos de um slot específico
  const getSlotAgendamentos = (date: Date, time: string): AgendamentoWithSchedule[] => {
    const dateStr = format(date, 'yyyy-MM-dd');
    const targetTime = time;

    return agendamentos.filter(agendamento => {
      if (!agendamento.scheduledTime) return false;

      // Verificar se o agendamento está agendado para esta data
      const isCorrectDate = selectedDateString === dateStr;
      if (!isCorrectDate) return false;

      // Extrair hora do scheduledTime
      const scheduledTime = agendamento.scheduledTime;

      return scheduledTime === targetTime;
    });
  };

  // Funções para drag & drop
  const handleDragOver = (e: React.DragEvent, date: Date, time: string) => {
    e.preventDefault();
    if (!draggedAgendamento || isPastDateTime(date, time) || isLunchTime(time)) return;

    setDragOverSlot({ date, time });
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    setDragOverSlot(null);
  };

  const handleDrop = (e: React.DragEvent, date: Date, time: string) => {
    e.preventDefault();
    setDragOverSlot(null);

    if (!draggedAgendamento || !onAgendamentoDrop) return;
    if (isPastDateTime(date, time) || isLunchTime(time)) return;

    onAgendamentoDrop(draggedAgendamento.id, date, time);
  };

  const isSlotHighlighted = (date: Date, time: string) => {
    return dragOverSlot &&
           isSameDay(dragOverSlot.date, date) &&
           dragOverSlot.time === time;
  };

  const canDropInSlot = (date: Date, time: string) => {
    if (!draggedAgendamento) return false;
    if (isPastDateTime(date, time)) return false;
    if (isLunchTime(time)) return false;

    // Verificar disponibilidade
    const availability = getDateAvailability(date);
    if (availability && availability.available === 0) return false;

    return true;
  };

  const getDateAvailability = (date: Date) => {
    if (!availabilityData || !technicianId) return null;
    const dateStr = format(date, 'yyyy-MM-dd');
    return availabilityData[dateStr];
  };

  const getTimeSlotClass = (date: Date, time: string) => {
    const isSelected = isSelectedDateTime(date, time);
    const isLunch = isLunchTime(time);
    const isPast = isPastDateTime(date, time);
    const availability = getDateAvailability(date);
    const hour = parseInt(time.split(':')[0]);
    const isHighlighted = isSlotHighlighted(date, time);
    const canDrop = canDropInSlot(date, time);

    let classes = 'flex items-center justify-center relative transition-all duration-200 ';

    if (isPast) {
      classes += 'bg-gray-50 text-gray-400 cursor-not-allowed ';
    } else if (isLunch) {
      classes += 'bg-gradient-to-r from-orange-50 to-yellow-50 ';
      if (draggedAgendamento) {
        classes += 'border-2 border-dashed border-red-300 ';
      }
    } else {
      // Estados de drag & drop
      if (draggedAgendamento) {
        if (canDrop) {
          classes += 'border-2 border-dashed border-green-400 bg-green-100 cursor-copy ';
          if (isHighlighted) {
            classes += 'bg-green-200 border-green-500 shadow-lg scale-105 ';
          }
        } else {
          classes += 'border-2 border-dashed border-red-300 bg-red-50 cursor-not-allowed ';
        }
      } else {
        classes += 'hover:bg-gray-50 ';
      }

      // Adicionar indicador de disponibilidade (apenas quando não está em modo drag)
      if (!draggedAgendamento && availability) {
        if (availability.available === 0) {
          classes += 'bg-red-50 ';
        } else if (availability.available < availability.total * 0.5) {
          classes += 'bg-yellow-50 ';
        } else {
          classes += 'bg-green-50 ';
        }
      }
    }

    return classes;
  };

  const formatWeekRange = () => {
    const start = weekDays[0];
    const end = weekDays[6];
    return `${format(start, 'dd/MM')} - ${format(end, 'dd/MM/yyyy')}`;
  };

  return (
    <div className={`bg-white border border-gray-200 rounded-xl shadow-sm transition-all duration-300 ${
      draggedAgendamento ? 'ring-2 ring-blue-300 ring-opacity-50 shadow-lg' : ''
    } ${className}`}>
      {/* Header do calendário */}
      <div className="flex items-center justify-between p-3 border-b border-gray-100">
        <div className="flex items-center gap-2">
          <CalendarIcon className="h-4 w-4 text-green-600" />
          <span className="text-sm font-medium text-gray-700">
            {draggedAgendamento ? 'Solte no horário desejado' : 'Selecionar Data e Horário'}
          </span>
          {draggedAgendamento && (
            <div className="ml-2 px-2 py-1 bg-blue-100 text-blue-700 rounded-full text-xs font-medium">
              Arrastando
            </div>
          )}
        </div>

        <div className="flex items-center gap-1">
          <button
            onClick={() => navigateWeek('prev')}
            className="p-1 hover:bg-gray-100 rounded-lg transition-colors"
            type="button"
          >
            <ChevronLeft className="h-3 w-3 text-gray-600" />
          </button>

          <span className="text-sm font-semibold text-gray-900 min-w-[120px] text-center">
            {formatWeekRange()}
          </span>

          <button
            onClick={() => navigateWeek('next')}
            className="p-1 hover:bg-gray-100 rounded-lg transition-colors"
            type="button"
          >
            <ChevronRight className="h-3 w-3 text-gray-600" />
          </button>
        </div>
      </div>

      {/* Calendário semanal - Estilo similar ao do técnico */}
      <div className="overflow-x-auto">
        <div className="min-w-[600px]">
          {/* Header dos dias */}
          <div className="grid grid-cols-8 border-b bg-gray-50">
            <div className="border-r bg-gray-100 p-2 text-center font-medium text-xs">
              Horário
            </div>
            {weekDays.map((day, index) => (
              <div key={index} className="p-2 text-center">
                <div className="text-xs text-gray-600 mb-1">
                  {format(day, 'EEE', { locale: ptBR })}
                </div>
                <div className={`text-sm font-semibold ${
                  isSameDay(day, new Date())
                    ? 'text-green-600 bg-green-100 rounded-full w-6 h-6 flex items-center justify-center mx-auto'
                    : 'text-gray-900'
                }`}>
                  {format(day, 'dd')}
                </div>
              </div>
            ))}
          </div>

          {/* Grid de horários */}
          <div className="overflow-y-visible">
            {timeSlots.map((time) => {
              const hour = parseInt(time.split(':')[0]);
              return (
                <div key={time} className="grid grid-cols-8 border-b min-h-[45px]">
                  <div className={`border-r p-1 text-center text-xs font-medium ${
                    hour === 12
                      ? 'bg-gradient-to-r from-orange-100 to-yellow-100 text-orange-700'
                      : 'bg-gray-50'
                  }`}>
                    {time}
                    {hour === 12 && (
                      <div className="text-xs text-orange-500 mt-0.5">Almoço</div>
                    )}
                  </div>
                  {weekDays.map((day, dayIndex) => (
                    <div
                      key={`${dayIndex}-${time}`}
                      className={`border-r p-1 min-h-[45px] cursor-pointer ${getTimeSlotClass(day, time)}`}
                      onClick={() => !isPastDateTime(day, time) && !draggedAgendamento && handleDateTimeClick(day, time)}
                      onDragOver={(e) => handleDragOver(e, day, time)}
                      onDragLeave={handleDragLeave}
                      onDrop={(e) => handleDrop(e, day, time)}
                      title={
                        isLunchTime(time)
                          ? 'Horário de Almoço'
                          : draggedAgendamento && !canDropInSlot(day, time)
                            ? 'Slot indisponível'
                            : `${format(day, 'dd/MM/yyyy')} às ${time}`
                      }
                    >
                      {/* Agendamentos no slot */}
                      {(() => {
                        const slotAgendamentos = getSlotAgendamentos(day, time);

                        if (slotAgendamentos.length > 0) {
                          return (
                            <div className="space-y-1 w-full">
                              {slotAgendamentos.slice(0, 2).map((agendamento, agendamentoIndex) => {
                                // Extrair primeiro e último nome
                                const nomeCompleto = agendamento.nome || '';
                                const partesNome = nomeCompleto.trim().split(' ');
                                const primeiroNome = partesNome[0] || '';
                                const ultimoNome = partesNome.length > 1 ? partesNome[partesNome.length - 1] : '';
                                const nomeExibicao = ultimoNome && ultimoNome !== primeiroNome
                                  ? `${primeiroNome} ${ultimoNome}`
                                  : primeiroNome;

                                // Limitar tamanho do nome
                                const nomeFormatado = nomeExibicao.length > 12
                                  ? `${nomeExibicao.substring(0, 12)}...`
                                  : nomeExibicao;

                                return (
                                  <div
                                    key={agendamento.id}
                                    draggable
                                    onDragStart={() => onAgendamentoDragStart && onAgendamentoDragStart(agendamento)}
                                    onDragEnd={() => onAgendamentoDragEnd && onAgendamentoDragEnd()}
                                    className={`px-2 py-1.5 rounded-md text-xs font-medium text-center transition-all duration-200 hover:scale-105 cursor-move ${
                                      agendamento.isSelected
                                        ? 'bg-gradient-to-r from-green-600 to-green-700 text-white border border-green-800 shadow-lg'
                                        : 'bg-gradient-to-r from-blue-50 to-blue-100 text-blue-900 border border-blue-300 hover:from-blue-100 hover:to-blue-200'
                                    } ${
                                      draggedAgendamento?.id === agendamento.id
                                        ? 'opacity-50 scale-95 border-dashed'
                                        : ''
                                    }`}
                                    title={`🎯 Pré-agendamento\n👤 Cliente: ${nomeCompleto}\n⏰ Horário: ${agendamento.scheduledTime || time}\n📍 Arraste para mover ou clique para detalhes`}
                                  >
                                    <div className="flex flex-col items-center space-y-0.5">
                                      <div className="flex items-center space-x-1">
                                        <DisplayNumber
                                          item={{...agendamento, isPreSchedule: true}}
                                          index={agendamento.sequenceOrder ? agendamento.sequenceOrder - 1 : agendamentoIndex}
                                          variant="inline"
                                          size="sm"
                                          className="text-[10px] font-bold text-white bg-green-600 px-1.5 py-0.5 rounded-full"
                                        />
                                      </div>
                                      <span className="font-semibold leading-tight text-[11px]">{nomeFormatado}</span>
                                      {agendamento.isSelected && (
                                        <div className="flex items-center space-x-1 mt-0.5">
                                          <Clock className="h-2 w-2" />
                                          <span className="text-[8px] opacity-90">{time}</span>
                                        </div>
                                      )}
                                    </div>
                                  </div>
                                );
                              })}

                              {/* Indicador de mais agendamentos */}
                              {slotAgendamentos.length > 2 && (
                                <div className="px-2 py-1 rounded-md text-[10px] font-medium text-center bg-gray-200 text-gray-700 border border-gray-300">
                                  +{slotAgendamentos.length - 2} mais
                                </div>
                              )}
                            </div>
                          );
                        }

                        // Conteúdo padrão quando não há agendamentos
                        if (isSelectedDateTime(day, time) && !draggedAgendamento) {
                          return (
                            <div className="bg-green-500 text-white p-1 rounded-md text-xs font-medium text-center">
                              ✓ Selecionado
                            </div>
                          );
                        }

                        return null;
                      })()}

                      {/* Indicador de drag over */}
                      {isSlotHighlighted(day, time) && canDropInSlot(day, time) && (
                        <div className="absolute inset-0 bg-green-400 bg-opacity-30 rounded-md flex items-center justify-center z-10">
                          <div className="bg-green-600 text-white px-2 py-1 rounded text-xs font-medium">
                            Soltar aqui
                          </div>
                        </div>
                      )}

                      {/* Indicador de slot inválido durante drag */}
                      {draggedAgendamento && !canDropInSlot(day, time) && (
                        <div className="absolute inset-0 flex items-center justify-center z-10">
                          <AlertTriangle className="h-4 w-4 text-red-500" />
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              );
            })}
          </div>
        </div>
      </div>

      {/* Footer com legenda */}
      <div className="p-3 border-t border-gray-100 bg-gray-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3 text-xs">
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 rounded-sm bg-green-400"></div>
              <span className="text-gray-600">Disponível</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 rounded-sm bg-yellow-400"></div>
              <span className="text-gray-600">Parcial</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 rounded-sm bg-red-400"></div>
              <span className="text-gray-600">Ocupado</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-2 h-2 rounded-sm bg-orange-400"></div>
              <span className="text-gray-600">Almoço</span>
            </div>
            {draggedAgendamento && (
              <div className="flex items-center gap-1 ml-2 px-2 py-1 bg-blue-100 rounded">
                <GripVertical className="h-3 w-3 text-blue-600" />
                <span className="text-blue-600 font-medium">Arrastando: {draggedAgendamento.name}</span>
              </div>
            )}
          </div>

          {isLoading && (
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 border-2 border-blue-200 border-t-blue-500 rounded-full animate-spin"></div>
              <span className="text-xs text-blue-600">Verificando...</span>
            </div>
          )}
        </div>

        {selectedDate && (
          <div className="mt-2 text-xs text-gray-500 text-center">
            Data e horário selecionados: <span className="font-medium text-green-600">
              {format(selectedDate, 'dd/MM/yyyy \'às\' HH:mm', { locale: ptBR })}
            </span>
          </div>
        )}
      </div>
    </div>
  );
};

export default WeeklyRouteCalendar;
