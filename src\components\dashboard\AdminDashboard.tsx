
import React from 'react';
import { ServiceOrder } from '@/types';
import AdminStatsCards from './AdminStatsCards';
import RecentServiceOrders from './RecentServiceOrders';
import SystemStatistics from './SystemStatistics';

interface AdminDashboardProps {
  serviceOrders: ServiceOrder[];
  pendingOrders: number;
  inProgressOrders: number;
  completedOrders: number;
  totalRevenue: number;
  pendingRevenue: number;
  clientCount: number;
  technicianCount: number;
  scheduledServicesCount: number;
  totalOrdersCount: number;
  formatCurrency: (amount: number) => string;
  formatDate: (dateString: string) => string;
  getStatusColor: (status: string) => string;
  getStatusLabel: (status: string) => string;
}

const AdminDashboard: React.FC<AdminDashboardProps> = ({
  serviceOrders,
  pendingOrders,
  inProgressOrders,
  completedOrders,
  totalRevenue,
  pendingRevenue,
  clientCount,
  technicianCount,
  scheduledServicesCount,
  totalOrdersCount,
  formatCurrency,
  formatDate,
  getStatusColor,
  getStatusLabel
}) => {
  return (
    <>
      <AdminStatsCards
        pendingOrders={pendingOrders}
        inProgressOrders={inProgressOrders}
        completedOrders={completedOrders}
        totalRevenue={totalRevenue}
        pendingRevenue={pendingRevenue}
        formatCurrency={formatCurrency}
      />
      
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        <RecentServiceOrders
          serviceOrders={serviceOrders}
          formatDate={formatDate}
          getStatusColor={getStatusColor}
          getStatusLabel={getStatusLabel}
        />
        
        <SystemStatistics
          clientCount={clientCount}
          technicianCount={technicianCount}
          scheduledServicesCount={scheduledServicesCount}
          totalOrdersCount={totalOrdersCount}
        />
      </div>
    </>
  );
};

export default AdminDashboard;
