.mapboxgl-map {
  font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', <PERSON><PERSON>, 'Helvetica Neue', Arial, sans-serif;
  border-radius: 0.5rem;
  overflow: hidden;
}

.mapboxgl-popup {
  max-width: 300px;
}

.mapboxgl-popup-content {
  padding: 0;
  border-radius: 0.5rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.mapboxgl-popup-close-button {
  font-size: 16px;
  color: #6b7280;
  padding: 5px 10px;
}

.mapboxgl-popup-close-button:hover {
  background-color: transparent;
  color: #111827;
}

.mapboxgl-ctrl-group {
  border-radius: 0.375rem;
  overflow: hidden;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.mapboxgl-ctrl-group button {
  width: 32px;
  height: 32px;
}

.mapboxgl-ctrl-group button:focus {
  box-shadow: none;
}

.custom-popup .mapboxgl-popup-content {
  padding: 0;
  border-radius: 0.5rem;
  overflow: hidden;
}

.custom-popup .mapboxgl-popup-close-button {
  color: #6b7280;
  font-size: 16px;
  padding: 5px 10px;
  z-index: 2;
}

.custom-popup .mapboxgl-popup-close-button:hover {
  background-color: transparent;
  color: #111827;
}

/* Estilo para o container do mapa */
.map-container {
  position: relative;
  border-radius: 0.5rem;
  overflow: hidden;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Estilo para o marcador personalizado */
.mapboxgl-marker {
  cursor: pointer;
}

/* Estilo para o controle de escala */
.mapboxgl-ctrl-scale {
  border-color: #6b7280;
  color: #6b7280;
  background-color: rgba(255, 255, 255, 0.8);
}

/* Estilo para o controle de fullscreen */
.mapboxgl-ctrl-fullscreen {
  background-image: url('data:image/svg+xml;charset=utf-8,%3Csvg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath d="M1 1v6h2V3h4V1H1zm16 0v2h4v4h2V1h-6zm0 16h4v-4h2v6h-6v-2zm-16 0v-2H1v-4H3v6h4v2H1z"/%3E%3C/svg%3E');
}

/* Estilo para o botão de zoom in */
.mapboxgl-ctrl-zoom-in {
  background-image: url('data:image/svg+xml;charset=utf-8,%3Csvg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath d="M10 4V1h1v3h3v1h-3v3H10V5H7V4h3z"/%3E%3Cpath d="M14 7H9v1h5v5h1V8h5V7h-5V2h-1v5z"/%3E%3C/svg%3E');
}

/* Estilo para o botão de zoom out */
.mapboxgl-ctrl-zoom-out {
  background-image: url('data:image/svg+xml;charset=utf-8,%3Csvg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath d="M7 10h11v1H7v-1z"/%3E%3C/svg%3E');
}

/* Estilo para o botão de compass */
.mapboxgl-ctrl-compass {
  background-image: url('data:image/svg+xml;charset=utf-8,%3Csvg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"%3E%3Cpath d="M9 2c-4.97 0-9 4.03-9 9s4.03 9 9 9 9-4.03 9-9-4.03-9-9-9zm0 16c-3.86 0-7-3.14-7-7s3.14-7 7-7 7 3.14 7 7-3.14 7-7 7z"/%3E%3Cpath d="M9 6c1.66 0 3 1.34 3 3s-1.34 3-3 3-3-1.34-3-3 1.34-3 3-3zm0 4c.55 0 1-.45 1-1s-.45-1-1-1-1 .45-1 1 .45 1 1 1z"/%3E%3C/svg%3E');
}
