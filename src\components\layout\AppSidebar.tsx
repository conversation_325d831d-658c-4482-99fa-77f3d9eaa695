import React from 'react';
import { useLocation } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';
import { usePermissions } from '@/hooks/usePermissions';
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarGroup,
  SidebarGroupContent,
  SidebarGroupLabel,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
  useSidebar,
} from '@/components/ui/sidebar';
import {
  LayoutDashboard,
  FileText,
  Calendar,
  Users,
  MapPin,
  DollarSign,
  Settings,
  Wrench,
  Package,
  Building,
  Clock,
  Plus,
  Route,
  CheckCircle,
  LogOut,
  User,
  ChevronRight,
  CreditCard,
  Truck
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

const AppSidebar: React.FC = () => {
  const { user, logout } = useAuth();
  const location = useLocation();
  const { isAdmin, isTechnician, isWorkshop, isClient } = usePermissions();
  const { open, isMobile } = useSidebar();

  const isActive = (path: string) => {
    return location.pathname === path || location.pathname.startsWith(path + '/');
  };

  // Estrutura hierárquica do menu para administradores
  const adminMenuStructure = [
    {
      category: 'Visão Geral',
      emoji: '📊',
      items: [
        { to: '/dashboard', icon: LayoutDashboard, label: 'Dashboard', emoji: '📊' }
      ]
    },
    {
      category: 'Operações',
      emoji: '⚙️',
      items: [
        { to: '/orders', icon: FileText, label: 'Ordens de Serviço', emoji: '📋' },
        { to: '/quotes', icon: CreditCard, label: 'Orçamentos', emoji: '💳' },
        { to: '/repairs', icon: Wrench, label: 'Reparos', emoji: '🔧' },
        { to: '/deliveries', icon: Truck, label: 'Entregas', emoji: '🚚' }
      ]
    },
    {
      category: 'Agendamento',
      emoji: '📅',
      items: [
        { to: '/main-calendar', icon: Calendar, label: 'Calendário', emoji: '📅' },
        { to: '/schedules', icon: Clock, label: 'Pré-Agendamentos', emoji: '⏰' },
        { to: '/routing', icon: Route, label: 'Roteirização', emoji: '🗺️' },
        { to: '/confirmations', icon: CheckCircle, label: 'Confirmações', emoji: '✅' }
      ]
    },
    {
      category: 'Gestão',
      emoji: '👥',
      items: [
        { to: '/clients', icon: Users, label: 'Clientes', emoji: '👥' },
        { to: '/technicians', icon: Wrench, label: 'Técnicos', emoji: '🔧' },
        { to: '/workshops', icon: Building, label: 'Oficinas', emoji: '🏭' }
      ]
    },
    {
      category: 'Monitoramento',
      emoji: '📍',
      items: [
        { to: '/tracking', icon: MapPin, label: 'Rastreamento', emoji: '📍' },
        { to: '/finance', icon: DollarSign, label: 'Financeiro', emoji: '💰' }
      ]
    },
    {
      category: 'Sistema',
      emoji: '⚙️',
      items: [
        { to: '/settings', icon: Settings, label: 'Configurações', emoji: '⚙️' }
      ]
    }
  ];

  // Estrutura hierárquica do menu para técnicos
  const technicianMenuStructure = [
    {
      category: 'Visão Geral',
      emoji: '📊',
      items: [
        { to: '/dashboard', icon: LayoutDashboard, label: 'Dashboard', emoji: '📊' }
      ]
    },
    {
      category: 'Trabalho',
      emoji: '📦',
      items: [
        { to: '/technician', icon: Package, label: 'Minhas Ordens', emoji: '📦' },
        { to: '/calendar', icon: Calendar, label: 'Meu Calendário', emoji: '📅' },
        { to: '/routing', icon: Route, label: 'Minha Rota', emoji: '🗺️' }
      ]
    }
  ];

  // Estrutura hierárquica do menu para oficinas
  const workshopMenuStructure = [
    {
      category: 'Visão Geral',
      emoji: '📊',
      items: [
        { to: '/dashboard', icon: LayoutDashboard, label: 'Dashboard', emoji: '📊' }
      ]
    },
    {
      category: 'Oficina',
      emoji: '🔧',
      items: [
        { to: '/orders', icon: FileText, label: 'Ordens na Oficina', emoji: '📋' },
        { to: '/calendar', icon: Calendar, label: 'Calendário', emoji: '📅' }
      ]
    }
  ];

  // Estrutura hierárquica do menu para clientes
  const clientMenuStructure = [
    {
      category: 'Visão Geral',
      emoji: '📊',
      items: [
        { to: '/dashboard', icon: LayoutDashboard, label: 'Dashboard', emoji: '📊' }
      ]
    },
    {
      category: 'Serviços',
      emoji: '📋',
      items: [
        { to: '/orders', icon: FileText, label: 'Meus Serviços', emoji: '📋' },
        { to: '/new-service', icon: Plus, label: 'Solicitar Serviço', emoji: '➕' }
      ]
    }
  ];

  // Determinar qual estrutura de menu usar com base na role do usuário
  let menuStructure = [];

  if (isAdmin()) {
    menuStructure = adminMenuStructure;
  } else if (isTechnician()) {
    menuStructure = technicianMenuStructure;
  } else if (isWorkshop()) {
    menuStructure = workshopMenuStructure;
  } else if (isClient()) {
    menuStructure = clientMenuStructure;
  }

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'bg-red-600/20 text-red-300 border-red-500/30';
      case 'technician':
        return 'bg-blue-600/20 text-blue-300 border-blue-500/30';
      case 'workshop':
        return 'bg-green-600/20 text-green-300 border-green-500/30';
      case 'client':
        return 'bg-purple-600/20 text-purple-300 border-purple-500/30';
      default:
        return 'bg-gray-600/20 text-gray-300 border-gray-500/30';
    }
  };

  const getRoleLabel = (role: string) => {
    switch (role) {
      case 'admin':
        return 'Administrador';
      case 'technician':
        return 'Técnico';
      case 'workshop':
        return 'Oficina';
      case 'client':
        return 'Cliente';
      default:
        return 'Usuário';
    }
  };

  return (
    <Sidebar collapsible="icon" className="bg-gradient-to-b from-gray-800 via-gray-800 to-gray-900 border-r border-gray-700 shadow-2xl">
      <SidebarHeader className="bg-gradient-to-r from-blue-600 to-blue-700 border-b border-blue-500/20">
        <div className="flex items-center gap-3 px-6 py-4 relative">
          <div className="flex aspect-square size-10 items-center justify-center rounded-xl bg-white/10 backdrop-blur-sm text-white shadow-lg">
            <Wrench className="size-5" />
          </div>
          <div className="grid flex-1 text-left leading-tight">
            <span className="truncate font-bold text-lg text-white">EletroFix Hub</span>
            <span className="truncate text-sm text-blue-100 opacity-90">Pro</span>
          </div>
          {/* Indicador visual quando colapsado */}
          {!open && (
            <div className="absolute -right-2 top-1/2 transform -translate-y-1/2 bg-blue-500 text-white rounded-full p-1 shadow-lg animate-pulse">
              <ChevronRight className="h-3 w-3" />
            </div>
          )}
        </div>
      </SidebarHeader>

      <SidebarContent>
        {/* Informações do usuário - sempre no mobile, apenas quando aberto no desktop */}
        {user && (isMobile || open) && (
          <SidebarGroup>
            <SidebarGroupLabel className={`text-gray-400 text-xs uppercase tracking-wider font-semibold px-6 py-2 ${isMobile ? 'data-[mobile=true]:!opacity-100 data-[mobile=true]:!mt-0' : ''}`}>
              Usuário
            </SidebarGroupLabel>
            <SidebarGroupContent>
              <div className="px-6 py-4 bg-gray-800/50 mx-4 rounded-lg border border-gray-700/50">
                <div className="flex items-center gap-3 mb-3">
                  <div className="flex items-center justify-center w-8 h-8 rounded-full bg-blue-600 text-white">
                    <User className="h-4 w-4" />
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium text-white break-words leading-tight">
                      {user.name || user.email}
                    </div>
                  </div>
                </div>
                <Badge
                  variant="outline"
                  className={`text-xs border-gray-600 ${getRoleBadgeColor(user.role)}`}
                >
                  {getRoleLabel(user.role)}
                </Badge>
              </div>
            </SidebarGroupContent>
          </SidebarGroup>
        )}

        {/* Menu de navegação hierárquico */}
        {menuStructure.map((section) => (
          <SidebarGroup key={section.category}>
            <SidebarGroupLabel className={`text-gray-400 text-xs uppercase tracking-wider font-semibold px-6 py-2 ${isMobile ? 'data-[mobile=true]:!opacity-100 data-[mobile=true]:!mt-0' : ''}`}>
              {(isMobile || open) ? (
                <>
                  <span className="mr-2">{section.emoji}</span>
                  {section.category}
                </>
              ) : (
                <span className="text-lg">{section.emoji}</span>
              )}
            </SidebarGroupLabel>
            <SidebarGroupContent>
              <SidebarMenu className={`${(isMobile || open) ? 'px-4' : 'px-2'} space-y-1`}>
                {section.items.map((item) => {
                  const Icon = item.icon;
                  const active = isActive(item.to);
                  return (
                    <SidebarMenuItem key={item.to}>
                      <SidebarMenuButton
                        asChild
                        isActive={active}
                        tooltip={item.label}
                        className={`
                          flex items-center ${(isMobile || open) ? 'gap-3 px-4 justify-start' : 'px-0 justify-center'} py-3 rounded-lg transition-all duration-200
                          ${active
                            ? 'bg-blue-600 text-white border-l-4 border-blue-400 shadow-lg'
                            : 'text-gray-300 hover:bg-gray-700/50 hover:text-white border-l-4 border-transparent'
                          }
                          ${isMobile ? 'data-[mobile=true]:!size-auto data-[mobile=true]:!p-3' : ''}
                        `}
                      >
                        <a href={item.to} className={`flex items-center w-full h-full ${(isMobile || open) ? 'gap-3' : 'justify-center'}`}>
                          {(isMobile || open) ? (
                            <>
                              <span className="text-lg flex-shrink-0">{item.emoji}</span>
                              <span className="font-medium">{item.label}</span>
                            </>
                          ) : (
                            <Icon className="h-5 w-5" />
                          )}
                        </a>
                      </SidebarMenuButton>
                    </SidebarMenuItem>
                  );
                })}
              </SidebarMenu>
            </SidebarGroupContent>
          </SidebarGroup>
        ))}
      </SidebarContent>

      <SidebarFooter className="border-t border-gray-700/50 bg-gray-800/30">
        <SidebarMenu className={`${(isMobile || open) ? 'px-4' : 'px-2'} py-2`}>
          <SidebarMenuItem>
            <SidebarMenuButton asChild>
              <Button
                variant="ghost"
                className={`w-full h-full ${(isMobile || open) ? 'justify-start' : 'justify-center'} text-gray-300 hover:text-white hover:bg-red-600/20 border border-gray-700/50 hover:border-red-500/50 transition-all duration-200 ${isMobile ? 'data-[mobile=true]:!size-auto data-[mobile=true]:!p-3' : ''}`}
                onClick={logout}
              >
                <LogOut className={`${(isMobile || open) ? 'h-4 w-4' : 'h-5 w-5'}`} />
                {(isMobile || open) && <span className="font-medium">Sair</span>}
              </Button>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>

      <SidebarRail />
    </Sidebar>
  );
};

export default AppSidebar;
