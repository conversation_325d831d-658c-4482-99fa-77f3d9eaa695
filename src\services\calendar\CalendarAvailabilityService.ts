import { supabase } from '@/integrations/supabase/client';
import { parseISO, format, isSameDay } from 'date-fns';
import { DEFAULT_CALENDAR_CONFIG } from '@/types/calendar';
import { calendarService } from './CalendarService';

export interface AvailabilitySlot {
  hour: number;
  isAvailable: boolean;
  reason?: string;
  existingAppointment?: {
    id: number;
    clientName: string;
    scheduledTime: string;
  };
}

export interface AvailabilityCheck {
  date: string;
  technicianId: string;
  slots: AvailabilitySlot[];
  totalAvailable: number;
  totalOccupied: number;
}

/**
 * Serviço para verificar disponibilidade no calendário principal
 */
class CalendarAvailabilityService {
  /**
   * Verifica disponibilidade de um técnico em uma data específica
   * Versão simplificada para evitar erros
   */
  async checkTechnicianAvailability(
    technicianId: string,
    date: string
  ): Promise<AvailabilityCheck> {
    try {
      console.log(`🔍 Verificando disponibilidade do técnico ${technicianId} em ${date}`);

      // Buscar ordens de serviço agendadas para este técnico nesta data
      const { data: serviceOrders, error } = await supabase
        .from('service_orders')
        .select('scheduled_date, scheduled_time, technician_id')
        .eq('technician_id', technicianId)
        .eq('scheduled_date', date)
        .not('scheduled_time', 'is', null);

      if (error) {
        console.error('Erro ao buscar ordens de serviço:', error);
      }

      const occupiedHours = new Set<number>();

      // Processar ordens de serviço existentes
      if (serviceOrders) {
        serviceOrders.forEach(order => {
          if (order.scheduled_time) {
            const hour = parseInt(order.scheduled_time.split(':')[0]);
            occupiedHours.add(hour);
            console.log(`📅 Slot ocupado encontrado: ${hour}:00 (OS: ${order.scheduled_time})`);
          }
        });
      }

      const slots: AvailabilitySlot[] = [];

      // Gerar slots para cada hora do dia de trabalho
      for (let hour = DEFAULT_CALENDAR_CONFIG.workStartHour; hour < DEFAULT_CALENDAR_CONFIG.workEndHour; hour++) {
        const slot: AvailabilitySlot = {
          hour,
          isAvailable: true
        };

        // Verificar se é horário de almoço
        if (hour >= DEFAULT_CALENDAR_CONFIG.lunchStartHour &&
            hour < DEFAULT_CALENDAR_CONFIG.lunchEndHour) {
          slot.isAvailable = false;
          slot.reason = 'Horário de almoço';
        }
        // Verificar se há ordem de serviço agendada neste horário
        else if (occupiedHours.has(hour)) {
          slot.isAvailable = false;
          slot.reason = 'Agendamento confirmado';
        }

        slots.push(slot);
      }

      const totalAvailable = slots.filter(slot => slot.isAvailable).length;
      const totalOccupied = slots.filter(slot => !slot.isAvailable).length;

      console.log(`✅ Disponibilidade verificada: ${totalAvailable} slots livres, ${totalOccupied} ocupados`);

      return {
        date,
        technicianId,
        slots,
        totalAvailable,
        totalOccupied
      };

    } catch (error) {
      console.error('Erro ao verificar disponibilidade:', error);
      // Retornar um resultado padrão em caso de erro
      const slots: AvailabilitySlot[] = [];
      for (let hour = DEFAULT_CALENDAR_CONFIG.workStartHour; hour < DEFAULT_CALENDAR_CONFIG.workEndHour; hour++) {
        slots.push({
          hour,
          isAvailable: hour < DEFAULT_CALENDAR_CONFIG.lunchStartHour || hour >= DEFAULT_CALENDAR_CONFIG.lunchEndHour,
          reason: hour >= DEFAULT_CALENDAR_CONFIG.lunchStartHour && hour < DEFAULT_CALENDAR_CONFIG.lunchEndHour ? 'Horário de almoço' : undefined
        });
      }

      return {
        date,
        technicianId,
        slots,
        totalAvailable: slots.filter(slot => slot.isAvailable).length,
        totalOccupied: slots.filter(slot => !slot.isAvailable).length
      };
    }
  }

  /**
   * Verifica se um horário específico está disponível
   * Versão simplificada para evitar erros
   */
  async isTimeSlotAvailable(
    technicianId: string,
    date: string,
    hour: number
  ): Promise<{ isAvailable: boolean; reason?: string }> {
    try {
      // Verificar se está dentro do horário de trabalho
      if (hour < DEFAULT_CALENDAR_CONFIG.workStartHour ||
          hour >= DEFAULT_CALENDAR_CONFIG.workEndHour) {
        return {
          isAvailable: false,
          reason: `Fora do horário de trabalho (${DEFAULT_CALENDAR_CONFIG.workStartHour}h-${DEFAULT_CALENDAR_CONFIG.workEndHour}h)`
        };
      }

      // Verificar se é horário de almoço
      if (hour >= DEFAULT_CALENDAR_CONFIG.lunchStartHour &&
          hour < DEFAULT_CALENDAR_CONFIG.lunchEndHour) {
        return {
          isAvailable: false,
          reason: 'Horário de almoço'
        };
      }

      // Verificar se há ordem de serviço agendada neste horário
      const { data: serviceOrders, error } = await supabase
        .from('service_orders')
        .select('id')
        .eq('technician_id', technicianId)
        .eq('scheduled_date', date)
        .like('scheduled_time', `${hour.toString().padStart(2, '0')}:%`)
        .limit(1);

      if (error) {
        console.error('Erro ao verificar slot específico:', error);
        return { isAvailable: true }; // Em caso de erro, assumir disponível
      }

      if (serviceOrders && serviceOrders.length > 0) {
        return {
          isAvailable: false,
          reason: 'Agendamento confirmado'
        };
      }

      return { isAvailable: true };

    } catch (error) {
      console.error('Erro ao verificar slot específico:', error);
      return {
        isAvailable: false,
        reason: 'Erro na verificação'
      };
    }
  }

  /**
   * Encontra próximos slots disponíveis
   */
  async findNextAvailableSlots(
    technicianId: string,
    date: string,
    count: number = 5
  ): Promise<AvailabilitySlot[]> {
    try {
      const availability = await this.checkTechnicianAvailability(technicianId, date);
      return availability.slots
        .filter(slot => slot.isAvailable)
        .slice(0, count);
    } catch (error) {
      console.error('Erro ao buscar slots disponíveis:', error);
      return [];
    }
  }

  /**
   * Valida múltiplos horários de uma vez
   */
  async validateMultipleTimeSlots(
    technicianId: string,
    date: string,
    requestedHours: number[]
  ): Promise<{ 
    isValid: boolean; 
    conflicts: string[]; 
    availableSlots: number[];
    conflictingSlots: number[];
  }> {
    try {
      const conflicts: string[] = [];
      const availableSlots: number[] = [];
      const conflictingSlots: number[] = [];

      for (const hour of requestedHours) {
        const check = await this.isTimeSlotAvailable(technicianId, date, hour);
        
        if (check.isAvailable) {
          availableSlots.push(hour);
        } else {
          conflictingSlots.push(hour);
          conflicts.push(`${hour}:00 - ${check.reason}`);
        }
      }

      return {
        isValid: conflicts.length === 0,
        conflicts,
        availableSlots,
        conflictingSlots
      };

    } catch (error) {
      console.error('Erro na validação múltipla:', error);
      return {
        isValid: false,
        conflicts: ['Erro interno na validação'],
        availableSlots: [],
        conflictingSlots: requestedHours
      };
    }
  }

  /**
   * Sugere horários alternativos em caso de conflito
   */
  async suggestAlternativeSlots(
    technicianId: string,
    date: string,
    conflictingHours: number[],
    count: number = 3
  ): Promise<number[]> {
    try {
      const availability = await this.checkTechnicianAvailability(technicianId, date);
      
      return availability.slots
        .filter(slot => 
          slot.isAvailable && 
          !conflictingHours.includes(slot.hour)
        )
        .map(slot => slot.hour)
        .slice(0, count);

    } catch (error) {
      console.error('Erro ao sugerir horários alternativos:', error);
      return [];
    }
  }
}

export const calendarAvailabilityService = new CalendarAvailabilityService();
