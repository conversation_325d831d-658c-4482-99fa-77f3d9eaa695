
import React from 'react';
import { ServiceOrderStatus } from '@/types';
import { DropdownMenuItem } from '@/components/ui/dropdown-menu';
import { ServiceFlowStep } from '@/utils/serviceFlowUtils';
import { Check } from 'lucide-react';

interface StatusDropdownItemsProps {
  serviceFlow: ServiceFlowStep[];
  currentStatus: ServiceOrderStatus;
  onStatusClick: (status: ServiceOrderStatus) => (e: React.MouseEvent) => void;
}

const StatusDropdownItems: React.FC<StatusDropdownItemsProps> = ({
  serviceFlow,
  currentStatus,
  onStatusClick
}) => {
  console.log(`🔍 [StatusDropdownItems] RENDERIZANDO - serviceFlow:`, serviceFlow.map(s => s.status));
  console.log(`🔍 [StatusDropdownItems] currentStatus:`, currentStatus);
  console.log(`🔍 [StatusDropdownItems] onStatusClick disponível:`, !!onStatusClick);

  return (
    <>
      {serviceFlow.map((step) => {
        const handleClick = async (e: React.MouseEvent) => {
          console.log(`🚨 [StatusDropdownItems] CLIQUE INTERCEPTADO! Status: ${step.status}`);
          console.log(`🚨 [StatusDropdownItems] Evento:`, { type: e.type, target: e.target });

          // Prevenir propagação
          e.preventDefault();
          e.stopPropagation();

          // Verificar se não é o status atual
          if (currentStatus === step.status) {
            console.log(`ℹ️ [StatusDropdownItems] Status já é ${step.status}, ignorando`);
            return;
          }

          try {
            // Chamar a função original
            const originalHandler = onStatusClick(step.status as ServiceOrderStatus);
            if (originalHandler) {
              console.log(`🔄 [StatusDropdownItems] Chamando handler original...`);
              await originalHandler(e);
              console.log(`✅ [StatusDropdownItems] Handler executado com sucesso!`);
            } else {
              console.error(`❌ [StatusDropdownItems] Handler original não retornado!`);
            }
          } catch (error) {
            console.error(`❌ [StatusDropdownItems] Erro ao executar handler:`, error);
          }
        };

        return (
          <DropdownMenuItem
            key={step.status}
            onClick={handleClick}
            disabled={currentStatus === step.status}
            className={`flex items-center justify-between gap-2 px-3 py-2 text-sm cursor-pointer
              ${currentStatus === step.status ? 'bg-muted opacity-50' : 'hover:bg-primary/5'}`}
            data-status={step.status}
          >
            <div className="flex items-center gap-2">
              {step.icon}
              <span>{step.label}</span>
            </div>
            {currentStatus === step.status && <Check className="h-4 w-4" />}
          </DropdownMenuItem>
        );
      })}
    </>
  );
};

export default StatusDropdownItems;
